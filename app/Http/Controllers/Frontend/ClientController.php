<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Publisher\app\Repositories\PublisherRepository;

class ClientController extends Controller
{
    protected $authorRepository;
    protected $publisherRepository;

    public function __construct(
        AuthorRepository $authorRepository,
        PublisherRepository $publisherRepository
    ) {
        $this->authorRepository = $authorRepository;
        $this->publisherRepository = $publisherRepository;
    }
    /**
     * Display all clients.
     */
    public function index(): View
    {
        // Get all active clients
        // $clients = Client::where('status', 'active')->orderBy('sort_order')->get();
        // $featuredClients = Client::where('status', 'active')->where('is_featured', true)->orderBy('sort_order')->limit(8)->get();

        return view('frontend.clients.index', [
            // 'clients' => $clients,
            // 'featuredClients' => $featuredClients,
        ]);
    }
}
