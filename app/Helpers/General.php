<?php

use App\Models\FileInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Nwidart\Modules\Facades\Module;
use App\Models\GlobalSettings;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Vite;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;

define("THUMB", "/thumb/");
define("IMAGE_FOLDER", "constants.file_manager.FOLDER");
define("FILESYSTEM_DEFAULT", "filesystems.default");

if (!function_exists('getStorageDisk')) {
    function getStorageDisk()
    {
        return Storage::disk(config('filesystems.default', 'public'));
    }
}

if (!function_exists('uploadFile')) {
    /**
     * upload file/image storage folder and their thumb image also
     * @param $file
     * @param $path
     * @param $filesystem
     * @param $width
     * @param $height
     * @param $thumb
     * @return string
     */
    function uploadFile($file, $path, $thumb = false, $width = 100, $height = 100)
    {
        $extension = $file->getClientOriginalExtension();
        $fileName = hash('sha256', $file->getFilename() . time()) . '.' . $extension;
        getStorageDisk()->put($path . '/' . $fileName, file_get_contents($file));

        if ($thumb === true) {
            $manager = new ImageManager(new GdDriver());
            $image = $manager->read($file)->scale(width: $width, height: $height);
            Storage::disk('public')->put($path . THUMB . $fileName, (string) $image->toJpeg());
        }
        addFileInfoIntoDB($fileName, $path);
        return $fileName;
    }
}

if (!function_exists('uploadBase64image')) {
    function uploadBase64image($image, $path, $thumb = false, $width = 100, $height = 100)
    {
        $base64Image = explode(";base64,", $image);
        $explodeImage = explode("image/", $base64Image[0]);
        $extension  = $explodeImage[1];
        $image_base64 = base64_decode($base64Image[1]);
        $file =  hash('sha256', uniqid()) . '.' . $extension;
        $filePath = $path . '/' . $file;
        getStorageDisk()->put($filePath, $image_base64);

        if ($thumb === true) {
            $manager = new ImageManager(new GdDriver());
            $image = $manager->read($image)->scale(width: $width, height: $height);
            Storage::disk('public')->put($path . THUMB . $file, (string) $image->toJpeg());
        }
        addFileInfoIntoDB($file, $path);
        return $file;
    }
}

if (!function_exists('deleteImage')) {
    /**
     * Delete uploaded file from server or cloud server
     *
     * @param $image
     * @param $path
     * @param $filesystem
     * @return boolean
     */
    function deleteImage($image, $path)
    {
        if (!empty($image) && getStorageDisk()->exists($path . $image)) {
            getStorageDisk()->delete($path . $image);
        }
        if (!empty($image) && getStorageDisk()->exists($path . THUMB . $image)) {
            getStorageDisk()->delete($path . THUMB . $image);
        }

        return true;
    }
}

if (!function_exists('uploadedMultipleBase64Images')) {
    function uploadedMultipleBase64Images($base64Files, $path, $fileNames = [])
    {

        foreach ($base64Files as $base64File) {
            // Extract file extension and base64 data
            if (preg_match('/^data:(.*?);base64,(.*)$/', $base64File, $matches)) {

                $fileData = base64_decode($matches[2]);

                // Use finfo to determine the MIME type from binary data
                $finfo = new finfo(FILEINFO_MIME_TYPE);
                $mimeType = $finfo->buffer($fileData);

                // Get the file extension based on the MIME type
                $fileExtension = substr(strrchr($mimeType, '/'), 1);

                // Generate a unique file name
                $fileName = uniqid('uploaded_file_', true) . '.' . $fileExtension;
                // Save the file to storage (e.g., 'public/uploads')
                Storage::put($path . $fileName, $fileData);

                // Save the file path for response or further processing
                $fileNames[] =  $fileName;
            }
        }
        return $fileNames;
    }
}

if (!function_exists('arrayMapRecursive')) {
    /**
     * @param $callback
     * @param $array
     *
     * @return array
     */
    function arrayMapRecursive($callback, $array)
    {
        $func = function ($item) use (&$func, &$callback) {
            return is_array($item) ? array_map($func, $item) : call_user_func($callback, $item);
        };

        return array_map($func, $array);
    }
}

if (!function_exists('getEnumValues')) {
    /**
     * @param $table
     * @param $column
     * @return array
     */
    function getEnumValues($table, $column)
    {
        $typeRes = DB::select("SHOW COLUMNS FROM $table WHERE Field = ?", [$column])[0];
        $typeRes = (array) $typeRes;
        $type = $typeRes['Type'];

        preg_match('/^enum\((.*)\)$/', $type, $matches);
        $enum = [];
        foreach (explode(',', $matches[1]) as $value) {
            $validValue = skipResolveOption($table, $value);
            if ($validValue) {
                $v = trim($value, "'");
                $enum = \Illuminate\Support\Arr::add($enum, ucfirst($v), ucfirst($v));
            }
        }
        return $enum;
    }
}

if (!function_exists('skipResolveOption')) {
    /**
     * @param $table
     * @param $value
     * @return bool
     */
    function skipResolveOption($table, $value)
    {
        $flag = true;
        if ($table === 'cases' && trim($value, "'") === 'In Progress') {
            $flag = false;
        }

        return $flag;
    }
}

if (!function_exists('formatAmount')) {
    /**
     * @param int|float $amount
     * @param int    $decimals
     * @param string $dec_point
     * @param string $thousands_sep
     * @param string $prefix
     * @param string $suffix
     *
     * @return string
     */
    function formatAmount($amount, $prefix = '$', $suffix = '')
    {
        $numberFormats = getNumberFormatSettings();
        $decimalPoint = getNumberFormat($numberFormats['decimalPoint']);
        return $prefix . number_format($amount, $numberFormats['decimals'], $decimalPoint, $numberFormats['separator']) . $suffix;
    }
}

if (!function_exists('getNumberFormat')) {
    /**
     * @param string $formateType
     * @return string
     *
     */

    function getNumberFormat($formateType)
    {
        if ($formateType == 'none') {
            $decimalPoint = '';
        } elseif ($formateType == 'space') {
            $decimalPoint = ' ';
        } else {
            $decimalPoint = $formateType;
        }

        return $decimalPoint;
    }
}

if (!function_exists('formatDate')) {
    /**
     * @param $date
     * @param $format
     * @return false|string
     */
    function formatDate($date, $format = null)
    {
        return \Carbon\Carbon::parse($date)->format($format ?? config('general.date_format'));
    }
}


/**
 * Get browser timezone, which is set in cookie
 */
function browserTimezone()
{
    if (isset($_COOKIE['browser_timezone'])) {
        $timezone = $_COOKIE['browser_timezone'];
    } else {
        $timezone = config('app.timezone');
    }

    return $timezone;
}

if (!function_exists('numberFormatShort')) {
    /**
     * @param int|float $n
     * @param int $precision
     *
     * @return string
     */
    // Converts a number into a short version, eg: 1000 -> 1k
    // Based on: http://stackoverflow.com/a/4371114
    function numberFormatShort($n, $precision = 1)
    {
        if ($n < 900) {
            // 0 - 900
            $n_format = number_format($n, $precision);
            $suffix = '';
        } elseif ($n < 900000) {
            // 0.9k-850k
            $n_format = number_format($n / 1000, $precision);
            $suffix = 'K';
        } elseif ($n < 900000000) {
            // 0.9m-850m
            $n_format = number_format($n / 1000000, $precision);
            $suffix = 'M';
        } elseif ($n < 900000000000) {
            // 0.9b-850b
            $n_format = number_format($n / 1000000000, $precision);
            $suffix = 'B';
        } else {
            // 0.9t+
            $n_format = number_format($n / 1000000000000, $precision);
            $suffix = 'T';
        }
        // Remove unnecessary zeroes after decimal. "1.0" -> "1"; "1.00" -> "1"
        // Intentionally does not affect partials, eg "1.50" -> "1.50"
        if ($precision > 0) {
            $dotZero = '.' . str_repeat('0', $precision);
            $n_format = str_replace($dotZero, '', $n_format);
        }

        return $n_format . $suffix;
    }
}

//Explode string with semicolon ;,for image
if (!function_exists('base64Extension')) {
    function base64Extension($str64)
    {
        return explode(';', explode('/', $str64)[1])[0];
    }
}

if (!function_exists('getRandomNumber')) {
    /**
     * generate random unique numbers
     *
     * @return void
     */
    function getRandomNumber()
    {
        return hexdec(uniqid());
    }
}

if (!function_exists('base64ResizeImage')) {
    /**
     * @param  $file
     * @param int $w
     * @param int $h
     *
     * @return image
     */
    function base64ResizeImage($file, $w, $h, $crop = false)
    {
        $src = imagecreatefromstring($file);
        if (!$src) {
            return false;
        }
        $width = imagesx($src);
        $height = imagesy($src);

        $r = $width / $height;
        if ($crop) {
            if ($width > $height) {
                $width = ceil($width - ($width * abs($r - $w / $h)));
            } else {
                $height = ceil($height - ($height * abs($r - $w / $h)));
            }
            $newWidth = $w;
            $newHeight = $h;
        } else {
            if ($w / $h > $r) {
                $newWidth = $h * $r;
                $newHeight = $h;
            } else {
                $newHeight = $w / $r;
                $newWidth = $w;
            }
        }

        $dst = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($dst, $src, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

        // Buffering
        ob_start();
        imagepng($dst);
        $data = ob_get_contents();
        ob_end_clean();

        return $data;
    }
}

if (!function_exists('utcFormatDate')) {
    /**
     * Convert date to UTC format
     *
     * @param  [type] $date   [description]
     * @param  [type] $format [description]
     * @return [type]         [description]
     */
    function utcFormatDate($date)
    {
        $test = browserTimezone();
        $objDate = \Carbon\Carbon::parse($date);
        $dateObj = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $objDate, $test);
        $dateObj->setTimezone('Asia/Kolkata');

        return $dateObj->toDateTimeString();
    }
}

if (!function_exists('adminDateFormatShow')) {
    /**
     * Display date according to admin formate
     *
     * @param  [type] $date   [description]
     * @param  [type] $format [description]
     */
    function adminDateFormatShow($date)
    {
        $getDateTimeSetting = getDateTimeSettings();
        $dateTimeFormat  = $getDateTimeSetting['date'];

        return \Carbon\Carbon::parse($date)
            ->timezone(browserTimezone())
            ->format($dateTimeFormat);
    }
}

if (!function_exists('adminDateTimeFormatShow')) {
    /**
     * Display date according to local timezone
     *
     * @param  [type] $date   [description]
     * @param  [type] $format [description]
     * @return [type]         [description]
     */
    function adminDateTimeFormatShow($date)
    {
        $getDateTimeSetting = getDateTimeSettings();
        $dateTimeFormat  = $getDateTimeSetting['date'] . ' ' . $getDateTimeSetting['time'];

        return \Carbon\Carbon::parse($date)
            ->timezone(browserTimezone())
            ->format($dateTimeFormat);
    }
}

if (!function_exists('adminNumberFormatShow')) {
    function adminNumberFormatShow($number)
    {
        $numberFormats = getNumberFormatSettings();
        $decimalPoint = getNumberFormat($numberFormats['decimalPoint']);
        return number_format($number, $numberFormats['decimals'], $decimalPoint, $numberFormats['separator']);
    }
}

if (!function_exists('siteLogo')) {
    /**
     * get site logo url
     *
     * @return [type] [description]
     */
    function siteLogo()
    {
        $path = config('constants.folders.setting');
        $logo = GlobalSettings::where('name', 'company_logo')->select('value')->first();
        if (!empty($logo->value) && getStorageDisk()->exists($path . '/' . $logo->value)) {
            return getImage($logo->value, $path);
        }
        return Vite::asset('resources/admin/images/svg-images/logo.svg');
    }
}

if (!function_exists('faviconLogo')) {
    /**
     * get favicon logo url
     *
     * @return [type] [description]
     */
    function faviconLogo()
    {
        $path = config('constants.folders.setting');
        $logo = GlobalSettings::where('name', 'company_favicon')->select('value')->first();
        if (!empty($logo->value) && getStorageDisk()->exists($path . '/' . $logo->value)) {
            return getImage($logo->value, $path);
        }

        return Vite::asset('resources/admin/images/svg-images/no-image.svg');
    }
}

if (!function_exists('getSocialMediaUrls')) {
    function getSocialMediaUrls()
    {
        $socialMediaUrls = GlobalSettings::whereIn('name', [
            'company_instagram_url',
            'company_facebook_url',
            'company_linkedin_url',
        ])->pluck('value', 'name')->toArray();

        return $socialMediaUrls;
    }
}

if (!function_exists('addButton')) {
    /**
     * return html of add button with given url
     *
     * @param string $url
     * @return string
     */
    function addButton($url, $module = '')
    {
        $routeName = Route::currentRouteName();

        $clsName = "inic inic-add";
        if ($routeName == 'admin.admins.index' || $routeName == 'admin.users.index') {
            $clsName = "inic inic-add-person me-1";
        }
        return '<a href="' . $url . '" class="btn btn-sm btn-primary glow-primary mr-3">
                <span class="' . $clsName . '"
                  data-bs-toggle="tooltip" title="Add ' . $module . ' "></span> Add ' . $module . '</a>';
    }
}

if (!function_exists('backButton')) {
    /**
     * return html of back button with given url
     *
     * @param string $url
     *
     * @return string
     */
    function backButton($url, $text = 'Back')
    {
        return '<a href="' . $url . '" class="btn btn-sm btn-primary glow-primary mr-3">
                <span class="inic inic-arrow-left me-1"
                data-bs-toggle="tooltip" title="' . $text . ' "></span> ' . $text . '</a>';
    }
}
if (!function_exists('adminFormatDate')) {
    /**

     * @param $date
     * @param $format
     * @return false|string
     */
    function adminFormatDate($date)
    {
        $getDateTimeSetting = getDateTimeSettings();

        return \Carbon\Carbon::parse($date)
            ->timezone(browserTimezone())
            ->format($getDateTimeSetting['date']);
    }
}
if (!function_exists('setResponse')) {
    /**
     * Set the response
     * @param  string $data    [data]
     * @param  object $extra_meta       [extra_meta]
     * @return void
     */
    function setResponse($meta = null)
    {
        $response = [];
        $response['data'] = (object) null;
        $response['extra_meta'] = (object) $meta;
        return $response;
    }
}
if (!function_exists('setErrorResponse')) {
    /**
     * Set the error message and alert type of message
     * @param  string $message    [description]
     * @param  object $meta       [meta]
     * @return void
     */
    function setErrorResponse($message = '', $meta = null)
    {
        $response = [];
        $response['error']['message'] = $message;
        $response['error']['meta'] = (object) $meta;
        return $response;
    }
}
if (!function_exists('setFlashMessage')) {
    /**
     * Set the flash message and alert type of message
     * @param  string $message    [description]
     * @param  string $alert_type [description]
     * @return void
     */
    function setFlashMessage($message, $alert_type = 'info')
    {
        session()->flash('alert-type', $alert_type);
        session()->flash('message', $message);
    }
}

//check file exist or not to path
if (!function_exists('getImage')) {
    function getImage($filename, $path, $thumb = false)
    {
        if (!empty($filename) && getStorageDisk()->exists($path . '/' . $filename)) {
            if ($thumb) {
                return getStorageDisk()->url($path . THUMB . $filename);
            }
            return getStorageDisk()->url($path . '/' . $filename);
        }
        return Vite::asset('resources/admin/images/default-user-img.png');
    }
}


if (!function_exists('addFileInfoIntoDB')) {
    function addFileInfoIntoDB($fileName, $path = 'uploads')
    {

        if (!empty($fileName)) {
            $file = FileInfo::where('name', $fileName)->first();
            if ($file) {
                $file->increment('usage_count');
            } else {
                // Get file mime-type and size
                $mimeType = getStorageDisk()->mimeType($path . '/' . $fileName);
                $size = getStorageDisk()->size($path . '/' . $fileName);

                FileInfo::create([
                    'name' => $fileName,
                    'file_type' => $path,
                    'mime_type' => $mimeType,
                    'disk' => config(FILESYSTEM_DEFAULT),
                    'size' => $size,
                    'usage_count' => 1,
                ]);
            }
        }
    }
}

//get all modules and check existing
if (!function_exists('checkModuleBased')) {
    function checkModuleBased($moduleName)
    {
        $allModules = Module::all();
        foreach ($allModules as $module) {
            $moduleArr[] = $module->getName();
        }

        //add all possible strings in array and find difference for name
        $singularWord1 = Str::singular($moduleName);

        $capitalizeWord1 = capitalizeAndRemoveDash($singularWord1);

        $moduleListArr[] = $capitalizeWord1;

        $arr = array_intersect(array_map('strtolower', $moduleArr), array_map('strtolower', $moduleListArr));

        if (!empty($arr)) {
            $moduleListArr[0] = $moduleArr[array_key_first($arr)];
            return $moduleListArr;
        }

        return $arr;
    }
}

//String convert into capitalize and also remove - from string
if (!function_exists('capitalizeAndRemoveDash')) {
    function capitalizeAndRemoveDash($str)
    {
        $str = str_replace('-', ' ', $str);
        $str = ucwords($str);
        $str = str_replace(' ', '', $str);
        return $str;
    }
}

//truncate table from db
if (!function_exists('truncateTable')) {
    function truncateTable($table)
    {
        try {
            DB::table($table)->truncate();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}

//Get grid column search status from setting table
if (!function_exists('gridColumnSearchStatus')) {
    function gridColumnSearchStatus()
    {
        try {
            return Cache::get('global_settings_cache')["column_search_status"] === "true" ? true : false;
        } catch (\Exception $e) {
            return false;
        }
    }
}

// Get date and time from setting table
if (!function_exists('getDateTimeSettings')) {
    function getDateTimeSettings()
    {
        try {
            $settings = Cache::get('global_settings_cache');
            return [
                'date' => $settings['date_format_set'] ?? config('general.default_date'),
                'time' => $settings['time_format_set'] ?? config('general.default_time')
            ];
        } catch (\Exception $e) {
            return $e->getMessage() ?? false;
        }
    }
}


// Get number format from setting table
if (!function_exists('getNumberFormatSettings')) {
    function getNumberFormatSettings()
    {
        try {
            $settings = Cache::get('global_settings_cache');
            return [
                'decimals'      => $settings['decimals'] ?? config('general.decimals'),
                'decimalPoint'  => $settings['decimalPoint'] ?? config('general.decimalPoint'),
                'separator'     => $settings['separator'] ?? config('general.separator'),
            ];
        } catch (\Exception $e) {
            return $e->getMessage() ?? false;
        }
    }
}

if (!function_exists('getDBTableRequiredCol')) {
    function getDBTableRequiredCol($tableName)
    {
        $results = DB::select("
            SELECT COLUMN_NAME
            FROM information_schema.columns
            WHERE TABLE_SCHEMA = ?
                AND TABLE_NAME = ?
                AND COLUMN_NAME != 'id'
                AND IS_NULLABLE = 'NO'
        ", [config('database.connections.mysql.database'), $tableName]);

        // Process the results
        $requiredCols = [];
        foreach ($results as $result) {
            // Output each non-nullable column name
            $requiredCols[] = $result->COLUMN_NAME;
        }
        return $requiredCols;
    }
}

if (!function_exists('isApiRequest')) {
    function isApiRequest()
    {
        return Str::startsWith(request()->route()?->getPrefix(), 'api')/*  ||
           request()->expectsJson() */;
    }
}
