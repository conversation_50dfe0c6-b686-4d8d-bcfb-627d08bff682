@extends('frontend.layouts.page')

@section('title', __('navigation.clients'))

@section('content')
    <!-- Client Hero Section -->
    <section class="relative bg-center bg-cover h-[350px] sm:h-[400px]"
        style="background-image: url('images/client-hero-section.webp');" aria-labelledby="client-main-title">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0.2)_20%,rgba(255,86,47,0.8)_172.67%)]">
        </div>

        <div class="relative z-10 container flex items-end h-full">
            <h1 id="client-main-title" class="text-[2rem] lg:text-[3rem] text-white mb-12 relative z-10">
                Client
            </h1>
        </div>
    </section>

    <!-- Client -->
    <section class="relative py-6 md:py-10 lg:py-20 bg-primary-100" aria-labelledby="client-list-heading">
        <div class="absolute top-0 left-0 w-full h-full opacity-25"
            style="background-image: url('images/section-pattern.svg');"></div>
        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[40%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[40%] bg-gradient-to-t from-white to-transparent z-0"></div>
        <div class="relative z-10 container mx-auto">
            <!-- Hidden heading for screen readers -->
            <h1 id="client-list-heading" class="sr-only">Client Directory - Browse Authors, Illustrators, and
                Publishers</h1>
            <!-- Filter and Search Section -->
            <div class="flex flex-col sm:flex-row justify-end items-center mb-8 md:mb-12 gap-4" role="search"
                aria-label="Filter and search clients">

                <!-- Custom Filter Selectbox -->
                <div class="relative">
                    <div class="custom-selectbox">
                        <button id="filter-selector"
                            class="selectbox-button group w-full rounded-none border border-gray-100 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20 px-6 py-4 text-gray-800 bg-white cursor-pointer flex items-center justify-between transition-all duration-200 hover:border-primary-500"
                            aria-expanded="false" aria-haspopup="listbox" aria-label="Filter clients by category">
                            <span
                                class="selectbox-text text-[1rem] leading-[1.4] font-normal group-hover:text-primary-500">FILTER</span>
                            <span
                                class="icon-arrow-down selectbox-arrow text-gray-800 group-hover:text-primary-500 ms-2"></span>
                        </button>
                        <div id="filter-menu" class="selectbox-dropdown w-[150px]">
                            <div class="selectbox-options py-2">
                                <button type="button" class="selectbox-option active" data-value="all">
                                    <span class="option-text">FILTER</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="alphabetical">
                                    <span class="option-text">Alphabetical</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="fiction">
                                    <span class="option-text">Fiction</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="non-fiction">
                                    <span class="option-text">Non-Fiction</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="children-ya">
                                    <span class="option-text">Children's & YA</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="estates">
                                    <span class="option-text">Estates</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="publisher">
                                    <span class="option-text">Publisher</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Search Input -->
                <div class="relative">
                    <label for="search-input" class="sr-only">Search clients by name</label>
                    <input type="text" placeholder="Search" id="search-input"
                        class="form-input sm:min-w-[400px] w-full ps-14 pe-6 py-4 rounded-full border border-gray-100 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20"
                        aria-label="Search clients by name" role="searchbox">
                    <span class="icon-search absolute start-[24px] top-1/2 transform -translate-y-1/2 text-gray-800"
                        aria-hidden="true"></span>
                </div>

            </div>

            <!-- Client Categories Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12" role="main" aria-label="Client categories">
                <!--Do something: if need illustrators column add this lg:grid-cols-3 -->
                <!-- Author Column -->
                <div class="text-center lg:text-left" aria-labelledby="authors-heading">
                    <h2 id="authors-heading" class="mb-6 leading-[1.4] text-[1.25rem] sm:text-[1.5rem] text-gray-800">
                        Authors</h2>
                    <nav aria-label="Author profiles" role="navigation">
                        <ul class="space-y-4" role="list">
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.3] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Ghaith Abdul-Ahad, Author" role="link">
                                    Ghaith Abdul-Ahad
                                </a>
                            </li>
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Ibrahim Al-Koni, Author" role="link">
                                    Ibrahim Al-Koni
                                </a>
                            </li>
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of John Agard, Author" role="link">
                                    John Agard
                                </a>
                            </li>
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Naja Marie Aidt, Author" role="link">
                                    Naja Marie Aidt
                                </a>
                            </li>
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Martin Aitken, Author" role="link">
                                    Martin Aitken
                                </a>
                            </li>
                            <li>
                                <a href="author-detail.html"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Decca Aitkenhead, Author" role="link">
                                    Decca Aitkenhead
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <div class="mt-8 text-right">
                        <a href="author-list.html" class="btn btn-primary btn-line-effect uppercase inline-block"
                            aria-label="View more authors">
                            View All
                        </a>
                    </div>
                </div>

                <!-- Publisher Column -->
                <div class="text-center lg:text-left" aria-labelledby="publishers-heading">
                    <h2 id="publishers-heading" class="mb-6 leading-[1.4] text-[1.25rem] sm:text-[1.5rem] text-gray-800">
                        Publishers</h2>
                    <nav aria-label="Publisher profiles" role="navigation">
                        <ul class="space-y-4" role="list">
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Ghaith Abdul-Ahad, Publisher" role="link">
                                    Ghaith Abdul-Ahad
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Ibrahim Al-Koni, Publisher" role="link">
                                    Ibrahim Al-Koni
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of John Agard, Publisher" role="link">
                                    John Agard
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Naja Marie Aidt, Publisher" role="link">
                                    Naja Marie Aidt
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Martin Aitken, Publisher" role="link">
                                    Martin Aitken
                                </a>
                            </li>
                            <li>
                                <a href="#"
                                    class="p-4 md:p-6 block bg-white border border-gray-100 font-normal text-gray-800 leading-[1.28] text-[1rem] sm:text-[1.125rem] hover:border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 outline-none"
                                    aria-label="View profile of Decca Aitkenhead, Publisher" role="link">
                                    Decca Aitkenhead
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <div class="mt-8 text-right">
                        <a href="publisher-list.html" class="btn btn-primary btn-line-effect uppercase inline-block"
                            aria-label="View more publishers">
                            View All
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </section>
@endsection
